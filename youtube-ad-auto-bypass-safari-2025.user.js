// ==UserScript==
// @name         YouTube Ad Auto-Bypass (Safari 2025)
// @namespace    https://augmentcode.com
// @version      2025.08.28
// @description  Neutralize YouTube ad payloads, auto-skip residual ads, and suppress enforcement popups. Works at document-start and across SPA navigations.
// @match        *://www.youtube.com/*
// @match        *://m.youtube.com/*
// @match        *://youtube.com/*
// @match        *://music.youtube.com/*
// @run-at       document-start
// @grant        unsafeWindow
// @grant        GM_addStyle
// ==/UserScript==

(() => {
  'use strict';

  const DEBUG = false;
  const dlog = (...args) => DEBUG && console.debug('[YT-AB]', ...args);

  // --- CSS: 隐藏弹窗/推广位/播放器广告容器 ---
  const css = `
/* 反广告拦截/执法弹窗与遮罩 */
ytd-enforcement-message-view-model,
ytd-enforcement-message-renderer,
ytd-player-error-message-renderer,
#consent-bump,
tp-yt-paper-dialog.ytd-popup-container {
  display: none !important;
}

/* 播放器广告层及容器 */
#player-ads, .ytp-ad-player-overlay, .ytp-ad-overlay-slot,
.ytp-ad-image-overlay, .ytp-ad-overlay-close-button,
.ytp-ad-skip-button, .ytp-ad-skip-button-modern,
.ytp-ad-text, .ytp-ad-preview-text, .ytp-ad-duration-remaining,
.ytp-paid-content-overlay {
  display: none !important;
}

/* 首页/信息流推广位与横幅 */
ytd-display-ad-renderer, ytd-ad-slot-renderer,
ytd-ad-inline-playback-renderer,
ytd-video-masthead-ad-v2-renderer,
ytd-promoted-sparkles-web-renderer,
ytd-action-companion-ad-renderer,
ytd-reel-shelf-renderer[is-ad],
ytd-rich-item-renderer[is-ad] {
  display: none !important;
}
  `;
  try {
    if (typeof GM_addStyle === 'function') GM_addStyle(css);
    else {
      const s = document.createElement('style');
      s.textContent = css;
      document.head ? document.head.appendChild(s) : document.documentElement.appendChild(s);
    }
  } catch (_) {}

  // --- 核心：净化广告字段 ---
  const AD_KEYS = new Set([
    'adPlacements', 'playerAds', 'adSlots', 'adBreaks', 'ad_load_flags',
    'ads', 'ad', 'prerollAllowed', 'midroll', 'adSignals'
  ]);
  function sanitize(obj, depth = 0) {
    if (!obj || typeof obj !== 'object' || depth > 6) return obj;

    // 专门路径清空
    if ('playerResponse' in obj) sanitize(obj.playerResponse, depth + 1);
    if ('adPlacements' in obj) obj.adPlacements = [];
    if ('playerAds' in obj) obj.playerAds = [];
    if ('adBreaks' in obj) obj.adBreaks = [];

    for (const k of Object.keys(obj)) {
      const v = obj[k];
      // 猜测性键名
      if (AD_KEYS.has(k) || /^ad(s|_|$)/i.test(k)) {
        if (Array.isArray(v)) obj[k] = [];
        else if (typeof v === 'object') obj[k] = {};
        else obj[k] = null;
        continue;
      }
      if (v && typeof v === 'object') sanitize(v, depth + 1);
    }
    // 常见旗标
    if ('isMonetized' in obj) obj.isMonetized = false;
    if ('is_monetized' in obj) obj.is_monetized = false;
    if ('playbackTracking' in obj && obj.playbackTracking?.videostatsPlaybackUrl) {
      // 保留追踪结构避免崩，但可移除广告追踪
      if (obj.playbackTracking?.adEventUrl) delete obj.playbackTracking.adEventUrl;
    }
    return obj;
  }

  // --- 需要净化的 URL 规则 ---
  function shouldSanitizeUrl(url = '') {
    if (!url) return false;
    try {
      const u = new URL(url, location.href);
      if (!/youtube\.com$|youtube\.com:/.test(u.host) && !/googlevideo\.com$/.test(u.host)) return false;
      return (
        u.pathname.includes('/youtubei/v1/player') ||
        u.pathname.includes('/youtubei/v1/next') ||
        u.pathname.includes('/youtubei/v1/browse') ||
        u.pathname.includes('/youtubei/v1/reel_watch_sequence') ||
        u.pathname.endsWith('/player') ||
        u.pathname.endsWith('/next') ||
        u.pathname.endsWith('/browse')
      );
    } catch {
      return false;
    }
  }

  // --- 往页面上下文注入补丁（保证 patch 影响到站内脚本） ---
  function inject(code) {
    const s = document.createElement('script');
    s.textContent = code;
    (document.head || document.documentElement).appendChild(s);
    s.parentNode && s.parentNode.removeChild(s);
  }

  function getPagePatchCode() {
    return `(() => {
      const dlog = ${DEBUG ? '(...a)=>console.debug("[YT-AB:page]",...a)' : '()=>{}'};

      const AD_KEYS = new Set(${JSON.stringify(Array.from(AD_KEYS))});
      function sanitize(obj, depth = 0) {
        if (!obj || typeof obj !== 'object' || depth > 6) return obj;
        if ('playerResponse' in obj) sanitize(obj.playerResponse, depth + 1);
        if ('adPlacements' in obj) obj.adPlacements = [];
        if ('playerAds' in obj) obj.playerAds = [];
        if ('adBreaks' in obj) obj.adBreaks = [];
        for (const k of Object.keys(obj)) {
          const v = obj[k];
          if (AD_KEYS.has(k) || /^ad(s|_|$)/i.test(k)) {
            if (Array.isArray(v)) obj[k] = [];
            else if (typeof v === 'object') obj[k] = {};
            else obj[k] = null;
            continue;
          }
          if (v && typeof v === 'object') sanitize(v, depth + 1);
        }
        if ('isMonetized' in obj) obj.isMonetized = false;
        if ('is_monetized' in obj) obj.is_monetized = false;
        return obj;
      }
      function shouldSanitizeUrl(url = '') {
        if (!url) return false;
        try {
          const u = new URL(url, location.href);
          if (!/youtube\\.com$|youtube\\.com:/.test(u.host) && !/googlevideo\\.com$/.test(u.host)) return false;
          return (
            u.pathname.includes('/youtubei/v1/player') ||
            u.pathname.includes('/youtubei/v1/next') ||
            u.pathname.includes('/youtubei/v1/browse') ||
            u.pathname.includes('/youtubei/v1/reel_watch_sequence') ||
            u.pathname.endsWith('/player') ||
            u.pathname.endsWith('/next') ||
            u.pathname.endsWith('/browse')
          );
        } catch {
          return false;
        }
      }

      // 1) 保护 ytInitialPlayerResponse / ytInitialData setter（最早阶段）
      try {
        const protect = (prop) => {
          const desc = Object.getOwnPropertyDescriptor(window, prop);
          let store;
          Object.defineProperty(window, prop, {
            configurable: true,
            enumerable: true,
            get() { return store; },
            set(v) {
              try { store = sanitize(v); }
              catch { store = v; }
            }
          });
          // 如果之前已经有值，尽量读取净化后回写
          if (desc && desc.value) {
            window[prop] = desc.value;
          }
        };
        protect('ytInitialPlayerResponse');
        protect('ytInitialData');
      } catch (e) { dlog('setter patch error', e); }

      // 2) fetch/Response 原型级补丁（覆盖 json() / text() 输出）
      try {
        const origFetch = window.fetch;
        const origJson = Response.prototype.json;
        const origText = Response.prototype.text;

        // 给 Response 打标签（是否需要净化）
        function tagResponse(res, url) {
          try {
            if (shouldSanitizeUrl(url || res.url)) {
              Object.defineProperty(res, '__ytab_patch__', { value: true, writable: false });
            }
          } catch {}
          return res;
        }

        window.fetch = new Proxy(origFetch, {
          apply(target, thisArg, args) {
            const [input] = args;
            const url = typeof input === 'string' ? input : (input && input.url);
            return Reflect.apply(target, thisArg, args).then((res) => tagResponse(res, url));
          }
        });

        Response.prototype.json = new Proxy(origJson, {
          apply(target, thisArg, args) {
            return Reflect.apply(target, thisArg, args).then((data) => {
              try {
                if (thisArg && thisArg.__ytab_patch__) {
                  return sanitize(data);
                }
              } catch {}
              return data;
            });
          }
        });

        Response.prototype.text = new Proxy(origText, {
          apply(target, thisArg, args) {
            return Reflect.apply(target, thisArg, args).then((txt) => {
              try {
                if (thisArg && thisArg.__ytab_patch__) {
                  // 大多数目标接口返回 JSON
                  try {
                    const data = JSON.parse(txt);
                    return JSON.stringify(sanitize(data));
                  } catch { /* 非 JSON 保持原样 */ }
                }
              } catch {}
              return txt;
            });
          }
        });
      } catch (e) { dlog('fetch/Response patch error', e); }

      // 3) 保守的 JSON.parse 兜底（仅用于页面中解析 API JSON 的路径）
      try {
        const origParse = JSON.parse;
        JSON.parse = new Proxy(origParse, {
          apply(target, thisArg, args) {
            const [str] = args;
            let out = Reflect.apply(target, thisArg, args);
            // 仅当像是 player/next/browse 结构时进行净化，避免全局降速
            try {
              if (out && typeof out === 'object') {
                const looksRelevant =
                  'playerResponse' in out ||
                  'adPlacements' in out ||
                  (out?.contents && out?.responseContext) ||
                  (out?.frameworkUpdates && out?.responseContext);
                if (looksRelevant) out = sanitize(out);
              }
            } catch {}
            return out;
          }
        });
      } catch (e) { dlog('JSON.parse patch error', e); }

      dlog('page patches installed');
    })();`;
  }

  // 注入页面补丁
  try {
    inject(getPagePatchCode());
  } catch (e) {
    dlog('inject failed', e);
  }

  // --- DOM 侧：自动跳过广告、处理导航 ---
  function trySkipAds() {
    // 1) 按钮直接点
    const btns = [
      '.ytp-ad-skip-button', '.ytp-ad-skip-button-modern',
      '.ytp-ad-overlay-close-button'
    ];
    for (const sel of btns) {
      const el = document.querySelector(sel);
      if (el) { el.click(); dlog('clicked', sel); }
    }

    // 2) 如果处于广告播放状态则加速并快进
    const player = document.querySelector('.html5-video-player');
    const isAd = player?.classList?.contains('ad-showing');
    const video = document.querySelector('video');

    if (isAd && video) {
      try {
        video.playbackRate = 16;
        // 快进到末尾
        if (isFinite(video.duration) && video.duration > 1) {
          video.currentTime = Math.max(0, video.duration - 0.1);
        }
      } catch {}
    } else if (video && video.playbackRate !== 1) {
      // 恢复正常倍速
      try { video.playbackRate = 1; } catch {}
    }
  }

  // 观察器：按钮/广告层出现即尝试跳过
  const mo = new MutationObserver(() => {
    trySkipAds();
  });
  const startObserve = () => {
    try {
      mo.observe(document.documentElement, { childList: true, subtree: true });
    } catch {}
  };
  startObserve();

  // 监听 YouTube SPA 导航事件，导航后继续工作
  const rearm = () => {
    setTimeout(() => {
      trySkipAds();
    }, 100);
  };
  window.addEventListener('yt-navigate-finish', rearm);
  window.addEventListener('spfdone', rearm);
  window.addEventListener('popstate', rearm);

  // 首次加载时也跑一遍
  document.addEventListener('DOMContentLoaded', trySkipAds);
  trySkipAds();
})();

